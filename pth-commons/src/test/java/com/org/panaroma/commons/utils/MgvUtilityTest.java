package com.org.panaroma.commons.utils;

import static com.org.panaroma.commons.constants.WebConstants.MGV_PURCHASED;
import static com.org.panaroma.commons.constants.WebConstants.MGV_REDEEM;
import static com.org.panaroma.commons.constants.WebConstants.TXN_PURPOSE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Test cases for MGV utility functions
 */
class MgvUtilityTest {

	private TransformedTransactionHistoryDetail mgvPurchaseTransaction;

	private TransformedTransactionHistoryDetail mgvRedemptionTransaction;

	private TransformedTransactionHistoryDetail nonMgvTransaction;

	@BeforeEach
	void setUp() {
		// Setup MGV purchase transaction
		mgvPurchaseTransaction = createMgvTransaction("user123", MGV_PURCHASED);

		// Setup MGV redemption transaction
		mgvRedemptionTransaction = createMgvTransaction("user123", MGV_REDEEM);

		// Setup non-MGV transaction
		nonMgvTransaction = createNonMgvTransaction("user123");
	}

	@Test
	void testIsMgvPurchase() {
		assertTrue(MgvUtility.isMgvPurchase(mgvPurchaseTransaction));
		assertFalse(MgvUtility.isMgvPurchase(mgvRedemptionTransaction));
		assertFalse(MgvUtility.isMgvPurchase(nonMgvTransaction));
		assertFalse(MgvUtility.isMgvPurchase(null));
	}

	@Test
	void testIsMgvRedemption() {
		assertTrue(MgvUtility.isMgvRedemption(mgvRedemptionTransaction));
		assertFalse(MgvUtility.isMgvRedemption(mgvPurchaseTransaction));
		assertFalse(MgvUtility.isMgvRedemption(nonMgvTransaction));
		assertFalse(MgvUtility.isMgvRedemption(null));
	}

	@Test
	void testIsMgvTransaction() {
		assertTrue(MgvUtility.isMgvTransaction(mgvPurchaseTransaction));
		assertTrue(MgvUtility.isMgvTransaction(mgvRedemptionTransaction));
		assertFalse(MgvUtility.isMgvTransaction(nonMgvTransaction));
		assertFalse(MgvUtility.isMgvTransaction(null));
	}

	@Test
	void testAggregateMgvTransactions() {
		// Create multiple MGV transactions for same order
		List<TransformedTransactionHistoryDetail> mgvTransactions = Arrays.asList(
				createMgvTransactionWithAmount("user123", MGV_REDEEM, 1000L),
				createMgvTransactionWithAmount("user123", MGV_REDEEM, 2000L),
				createMgvTransactionWithAmount("user123", MGV_REDEEM, 1500L));

		TransformedTransactionHistoryDetail aggregated = MgvUtility.aggregateMgvTransactions(mgvTransactions);

		assertNotNull(aggregated);
		assertEquals(4500L, aggregated.getAmount()); // 1000 + 2000 + 1500
		assertNotNull(aggregated.getContextMap());
		assertEquals("true", aggregated.getContextMap().get("mgv_aggregated"));
		assertEquals("3", aggregated.getContextMap().get("mgv_voucher_count"));
	}

	@Test
	void testAggregateSingleMgvTransaction() {
		List<TransformedTransactionHistoryDetail> singleTransaction = Arrays.asList(mgvRedemptionTransaction);

		TransformedTransactionHistoryDetail result = MgvUtility.aggregateMgvTransactions(singleTransaction);

		assertEquals(mgvRedemptionTransaction, result);
	}

	@Test
	void testAggregateEmptyList() {
		TransformedTransactionHistoryDetail result = MgvUtility.aggregateMgvTransactions(new ArrayList<>());
		assertEquals(null, result);

		result = MgvUtility.aggregateMgvTransactions(null);
		assertEquals(null, result);
	}

	@Test
	void testGroupMgvTransactionsByOrderId() {
		List<TransformedTransactionHistoryDetail> transactions = Arrays.asList(mgvRedemptionTransaction,
				mgvPurchaseTransaction, // Should not be included (only redemptions)
				nonMgvTransaction);

		Map<String, List<TransformedTransactionHistoryDetail>> grouped = MgvUtility
			.groupMgvTransactionsByOrderId(transactions);

		assertEquals(1, grouped.size());
		assertTrue(grouped.containsKey(mgvRedemptionTransaction.getTxnId()));
		assertEquals(1, grouped.get(mgvRedemptionTransaction.getTxnId()).size());
	}

	@Test
	void testShouldAggregateMgvTransactions() {
		assertTrue(MgvUtility.shouldAggregateMgvTransactions());
	}

	@Test
	void testGetVoucherNames() {
		List<TransformedTransactionHistoryDetail> transactions = Arrays.asList(
				createMgvTransactionWithVoucherName("user123", MGV_REDEEM, "Amazon Voucher"),
				createMgvTransactionWithVoucherName("user123", MGV_REDEEM, "Flipkart Voucher"));

		List<String> voucherNames = MgvUtility.getVoucherNames(transactions);

		assertEquals(2, voucherNames.size());
		assertTrue(voucherNames.contains("Amazon Voucher"));
		assertTrue(voucherNames.contains("Flipkart Voucher"));
	}

	// Helper methods for creating test data

	private TransformedTransactionHistoryDetail createMgvTransaction(String entityId, String txnPurpose) {
		return createMgvTransactionWithAmount(entityId, txnPurpose, 1000L);
	}

	private TransformedTransactionHistoryDetail createMgvTransactionWithAmount(String entityId, String txnPurpose,
			Long amount) {
		TransformedTransactionHistoryDetail txn = new TransformedTransactionHistoryDetail();
		txn.setEntityId(entityId);
		txn.setTxnId("txn_" + System.currentTimeMillis());
		txn.setAmount(amount);

		TransformedParticipant participant = new TransformedParticipant();
		participant.setEntityId(entityId);
		participant.setPaymentSystem(PaymentSystemEnum.MGV.getPaymentSystemKey());
		participant.setAmount(amount);

		Map<String, String> contextMap = new HashMap<>();
		contextMap.put(TXN_PURPOSE, txnPurpose);
		participant.setContextMap(contextMap);

		txn.setParticipants(Arrays.asList(participant));
		return txn;
	}

	private TransformedTransactionHistoryDetail createMgvTransactionWithVoucherName(String entityId, String txnPurpose,
			String voucherName) {
		TransformedTransactionHistoryDetail txn = createMgvTransaction(entityId, txnPurpose);

		// Add voucher name to other data
		TransformedParticipant participant = txn.getParticipants().get(0);
		if (participant.getOtherData() == null) {
			participant.setOtherData(new com.org.panaroma.commons.dto.es.OtherData());
		}
		participant.getOtherData().setName(voucherName);

		return txn;
	}

	private TransformedTransactionHistoryDetail createNonMgvTransaction(String entityId) {
		TransformedTransactionHistoryDetail txn = new TransformedTransactionHistoryDetail();
		txn.setEntityId(entityId);
		txn.setTxnId("txn_" + System.currentTimeMillis());
		txn.setAmount(1000L);

		TransformedParticipant participant = new TransformedParticipant();
		participant.setEntityId(entityId);
		participant.setPaymentSystem(PaymentSystemEnum.UPI.getPaymentSystemKey());
		participant.setAmount(1000L);

		txn.setParticipants(Arrays.asList(participant));
		return txn;
	}

}
