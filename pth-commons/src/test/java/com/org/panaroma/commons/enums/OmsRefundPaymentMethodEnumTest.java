package com.org.panaroma.commons.enums;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.org.panaroma.commons.dto.PaymentSystemEnum;
import org.junit.jupiter.api.Test;

/**
 * Test cases for OMS Refund Payment Method Enum including MGV support
 */
class OmsRefundPaymentMethodEnumTest {

	@Test
	void testMgvRefundPaymentMethodExists() {
		OmsRefundPaymentMethodEnum mgv = OmsRefundPaymentMethodEnum.MGV;

		assertNotNull(mgv);
		assertEquals("MGV", mgv.getKey());
		assertEquals("Merchant Gift Voucher", mgv.getDisplayName());
		assertEquals(PaymentSystemEnum.MGV, mgv.getPaymentSystemEnum());
	}

	@Test
	void testAllRefundPaymentMethodsHaveValidPaymentSystems() {
		for (OmsRefundPaymentMethodEnum paymentMethod : OmsRefundPaymentMethodEnum.values()) {
			assertNotNull(paymentMethod.getKey());
			assertNotNull(paymentMethod.getDisplayName());
			assertNotNull(paymentMethod.getPaymentSystemEnum());
		}
	}

	@Test
	void testMgvRefundPaymentMethodMapping() {
		// Test that MGV refund maps to correct payment system
		assertEquals(PaymentSystemEnum.MGV, OmsRefundPaymentMethodEnum.MGV.getPaymentSystemEnum());
		assertEquals(6, PaymentSystemEnum.MGV.getPaymentSystemKey().intValue());
	}

	@Test
	void testGetRefundPaymentMethodByKey() {
		OmsRefundPaymentMethodEnum mgv = null;
		for (OmsRefundPaymentMethodEnum method : OmsRefundPaymentMethodEnum.values()) {
			if ("MGV".equals(method.getKey())) {
				mgv = method;
				break;
			}
		}

		assertNotNull(mgv);
		assertEquals(OmsRefundPaymentMethodEnum.MGV, mgv);
	}

}
