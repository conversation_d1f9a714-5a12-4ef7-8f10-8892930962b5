package com.org.panaroma.commons.enums;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.org.panaroma.commons.dto.PaymentSystemEnum;
import org.junit.jupiter.api.Test;

/**
 * Test cases for OMS Payment Method Enum including MGV support
 */
class OmsPaymentMethodEnumTest {

	@Test
	void testMgvPaymentMethodExists() {
		OmsPaymentMethodEnum mgv = OmsPaymentMethodEnum.MGV;

		assertNotNull(mgv);
		assertEquals("MGV", mgv.getKey());
		assertEquals("Merchant Gift Voucher", mgv.getDisplayName());
		assertEquals(PaymentSystemEnum.MGV, mgv.getPaymentSystemEnum());
	}

	@Test
	void testAllPaymentMethodsHaveValidPaymentSystems() {
		for (OmsPaymentMethodEnum paymentMethod : OmsPaymentMethodEnum.values()) {
			assertNotNull(paymentMethod.getKey());
			assertNotNull(paymentMethod.getDisplayName());
			assertNotNull(paymentMethod.getPaymentSystemEnum());
		}
	}

	@Test
	void testMgvPaymentMethodMapping() {
		// Test that MGV maps to correct payment system
		assertEquals(PaymentSystemEnum.MGV, OmsPaymentMethodEnum.MGV.getPaymentSystemEnum());
		assertEquals(6, PaymentSystemEnum.MGV.getPaymentSystemKey().intValue());
	}

	@Test
	void testGetPaymentMethodByKey() {
		OmsPaymentMethodEnum mgv = null;
		for (OmsPaymentMethodEnum method : OmsPaymentMethodEnum.values()) {
			if ("MGV".equals(method.getKey())) {
				mgv = method;
				break;
			}
		}

		assertNotNull(mgv);
		assertEquals(OmsPaymentMethodEnum.MGV, mgv);
	}

}
