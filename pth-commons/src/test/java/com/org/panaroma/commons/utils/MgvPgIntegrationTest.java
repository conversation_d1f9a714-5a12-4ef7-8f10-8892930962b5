package com.org.panaroma.commons.utils;

import static com.org.panaroma.commons.constants.WebConstants.MGV_REDEEM;
import static com.org.panaroma.commons.constants.WebConstants.TXN_PURPOSE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.es.TransformedMerchantData;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.es.TransformedUPIData;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;

/**
 * Test cases for MGV PG integration including merchant VPA/MID handling
 */
public class MgvPgIntegrationTest {

	private TransformedTransactionHistoryDetail mgvTransactionWithMerchantVpa;

	private TransformedTransactionHistoryDetail mgvTransactionWithMerchantId;

	private TransformedTransactionHistoryDetail mgvTransactionWithBothVpaAndId;

	private TransformedTransactionHistoryDetail nonMgvTransaction;

	@Before
	public void setUp() {
		// Setup MGV transaction with merchant VPA
		mgvTransactionWithMerchantVpa = createMgvTransactionWithMerchantVpa("user123", "merchant@paytm", null);

		// Setup MGV transaction with merchant ID
		mgvTransactionWithMerchantId = createMgvTransactionWithMerchantId("user123", null, "MERCHANT_123");

		// Setup MGV transaction with both VPA and ID
		mgvTransactionWithBothVpaAndId = createMgvTransactionWithMerchantVpa("user123", "merchant@paytm",
				"MERCHANT_123");

		// Setup non-MGV transaction
		nonMgvTransaction = createNonMgvTransaction("user123");
	}

	@Test
	public void testGetMerchantVpaFromMgvTransaction() {
		String vpa = MgvUtility.getMerchantVpaFromMgvTransaction(mgvTransactionWithMerchantVpa);
		assertEquals("merchant@paytm", vpa);

		vpa = MgvUtility.getMerchantVpaFromMgvTransaction(mgvTransactionWithMerchantId);
		assertNull(vpa);

		vpa = MgvUtility.getMerchantVpaFromMgvTransaction(nonMgvTransaction);
		assertNull(vpa);

		vpa = MgvUtility.getMerchantVpaFromMgvTransaction(null);
		assertNull(vpa);
	}

	@Test
	public void testGetMerchantIdFromMgvTransaction() {
		String merchantId = MgvUtility.getMerchantIdFromMgvTransaction(mgvTransactionWithMerchantId);
		assertEquals("MERCHANT_123", merchantId);

		merchantId = MgvUtility.getMerchantIdFromMgvTransaction(mgvTransactionWithMerchantVpa);
		assertNull(merchantId);

		merchantId = MgvUtility.getMerchantIdFromMgvTransaction(nonMgvTransaction);
		assertNull(merchantId);

		merchantId = MgvUtility.getMerchantIdFromMgvTransaction(null);
		assertNull(merchantId);
	}

	@Test
	public void testGetMerchantParticipantFromMgvTransaction() {
		TransformedParticipant merchant = MgvUtility
			.getMerchantParticipantFromMgvTransaction(mgvTransactionWithMerchantVpa);
		assertNotNull(merchant);
		assertEquals(EntityTypesEnum.MERCHANT.getEntityTypeKey(), merchant.getEntityType());

		merchant = MgvUtility.getMerchantParticipantFromMgvTransaction(nonMgvTransaction);
		assertNull(merchant);

		merchant = MgvUtility.getMerchantParticipantFromMgvTransaction(null);
		assertNull(merchant);
	}

	@Test
	public void testIsValidMgvMerchantData() {
		TransformedParticipant merchantWithVpa = createMerchantParticipantWithVpa("merchant@paytm");
		assertTrue(MgvUtility.isValidMgvMerchantData(merchantWithVpa));

		TransformedParticipant merchantWithId = createMerchantParticipantWithId("MERCHANT_123");
		assertTrue(MgvUtility.isValidMgvMerchantData(merchantWithId));

		TransformedParticipant merchantWithBoth = createMerchantParticipantWithBoth("merchant@paytm", "MERCHANT_123");
		assertTrue(MgvUtility.isValidMgvMerchantData(merchantWithBoth));

		TransformedParticipant merchantWithNeither = createMerchantParticipantWithNeither();
		assertFalse(MgvUtility.isValidMgvMerchantData(merchantWithNeither));

		assertFalse(MgvUtility.isValidMgvMerchantData(null));
	}

	@Test
	public void testCreateMgvMerchantContext() {
		TransformedParticipant merchantWithVpa = createMerchantParticipantWithVpa("merchant@paytm");
		Map<String, String> context = MgvUtility.createMgvMerchantContext(merchantWithVpa);

		assertNotNull(context);
		assertEquals("merchant@paytm", context.get("merchant_vpa"));
		assertNull(context.get("merchant_id"));

		TransformedParticipant merchantWithId = createMerchantParticipantWithId("MERCHANT_123");
		context = MgvUtility.createMgvMerchantContext(merchantWithId);

		assertNotNull(context);
		assertEquals("MERCHANT_123", context.get("merchant_id"));
		assertNull(context.get("merchant_vpa"));

		TransformedParticipant merchantWithBoth = createMerchantParticipantWithBoth("merchant@paytm", "MERCHANT_123");
		context = MgvUtility.createMgvMerchantContext(merchantWithBoth);

		assertNotNull(context);
		assertEquals("merchant@paytm", context.get("merchant_vpa"));
		assertEquals("MERCHANT_123", context.get("merchant_id"));

		context = MgvUtility.createMgvMerchantContext(null);
		assertNotNull(context);
		assertTrue(context.isEmpty());
	}

	@Test
	public void testMgvTransactionWithBothVpaAndId() {
		String vpa = MgvUtility.getMerchantVpaFromMgvTransaction(mgvTransactionWithBothVpaAndId);
		assertEquals("merchant@paytm", vpa);

		String merchantId = MgvUtility.getMerchantIdFromMgvTransaction(mgvTransactionWithBothVpaAndId);
		assertEquals("MERCHANT_123", merchantId);

		TransformedParticipant merchant = MgvUtility
			.getMerchantParticipantFromMgvTransaction(mgvTransactionWithBothVpaAndId);
		assertTrue(MgvUtility.isValidMgvMerchantData(merchant));
	}

	// Helper methods for creating test data

	private TransformedTransactionHistoryDetail createMgvTransactionWithMerchantVpa(String userId, String merchantVpa,
			String merchantId) {
		TransformedTransactionHistoryDetail txn = new TransformedTransactionHistoryDetail();
		txn.setEntityId(userId);
		txn.setTxnId("txn_" + System.currentTimeMillis());

		// User participant
		TransformedParticipant userParticipant = new TransformedParticipant();
		userParticipant.setEntityId(userId);
		userParticipant.setEntityType(EntityTypesEnum.USER.getEntityTypeKey());
		userParticipant.setPaymentSystem(PaymentSystemEnum.MGV.getPaymentSystemKey());

		Map<String, String> userContext = new HashMap<>();
		userContext.put(TXN_PURPOSE, MGV_REDEEM);
		userParticipant.setContextMap(userContext);

		// Merchant participant
		TransformedParticipant merchantParticipant = new TransformedParticipant();
		merchantParticipant.setEntityId("merchant_entity_id");
		merchantParticipant.setEntityType(EntityTypesEnum.MERCHANT.getEntityTypeKey());
		merchantParticipant.setPaymentSystem(PaymentSystemEnum.UPI.getPaymentSystemKey());

		if (merchantVpa != null) {
			TransformedUPIData upiData = new TransformedUPIData();
			upiData.setVpa(merchantVpa);
			merchantParticipant.setUpiData(upiData);
		}

		if (merchantId != null) {
			TransformedMerchantData merchantData = new TransformedMerchantData();
			merchantData.setMerchantId(merchantId);
			merchantParticipant.setMerchantData(merchantData);
		}

		txn.setParticipants(Arrays.asList(userParticipant, merchantParticipant));
		return txn;
	}

	private TransformedTransactionHistoryDetail createMgvTransactionWithMerchantId(String userId, String merchantVpa,
			String merchantId) {
		return createMgvTransactionWithMerchantVpa(userId, merchantVpa, merchantId);
	}

	private TransformedTransactionHistoryDetail createNonMgvTransaction(String userId) {
		TransformedTransactionHistoryDetail txn = new TransformedTransactionHistoryDetail();
		txn.setEntityId(userId);
		txn.setTxnId("txn_" + System.currentTimeMillis());

		TransformedParticipant userParticipant = new TransformedParticipant();
		userParticipant.setEntityId(userId);
		userParticipant.setEntityType(EntityTypesEnum.USER.getEntityTypeKey());
		userParticipant.setPaymentSystem(PaymentSystemEnum.UPI.getPaymentSystemKey());

		txn.setParticipants(Arrays.asList(userParticipant));
		return txn;
	}

	private TransformedParticipant createMerchantParticipantWithVpa(String vpa) {
		TransformedParticipant participant = new TransformedParticipant();
		participant.setEntityType(EntityTypesEnum.MERCHANT.getEntityTypeKey());

		TransformedUPIData upiData = new TransformedUPIData();
		upiData.setVpa(vpa);
		participant.setUpiData(upiData);

		return participant;
	}

	private TransformedParticipant createMerchantParticipantWithId(String merchantId) {
		TransformedParticipant participant = new TransformedParticipant();
		participant.setEntityType(EntityTypesEnum.MERCHANT.getEntityTypeKey());

		TransformedMerchantData merchantData = new TransformedMerchantData();
		merchantData.setMerchantId(merchantId);
		participant.setMerchantData(merchantData);

		return participant;
	}

	private TransformedParticipant createMerchantParticipantWithBoth(String vpa, String merchantId) {
		TransformedParticipant participant = new TransformedParticipant();
		participant.setEntityType(EntityTypesEnum.MERCHANT.getEntityTypeKey());

		TransformedUPIData upiData = new TransformedUPIData();
		upiData.setVpa(vpa);
		participant.setUpiData(upiData);

		TransformedMerchantData merchantData = new TransformedMerchantData();
		merchantData.setMerchantId(merchantId);
		participant.setMerchantData(merchantData);

		return participant;
	}

	private TransformedParticipant createMerchantParticipantWithNeither() {
		TransformedParticipant participant = new TransformedParticipant();
		participant.setEntityType(EntityTypesEnum.MERCHANT.getEntityTypeKey());
		return participant;
	}

}
