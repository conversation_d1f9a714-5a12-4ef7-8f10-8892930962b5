# MGV PG Integration Documentation

## Overview
This document describes the MGV (Merchant Gift Voucher) integration with PG (Payment Gateway) for handling MGV redemption transactions. The integration follows the existing wallet transaction pattern with specific modifications for MGV merchant data handling.

## Key Changes Implemented

### 1. Payment System Mapping
**Location**: `flink-ingestion-service/src/main/java/com/org/panaroma/ingester/transformer/TransformerHelper.java`

- **GV to MGV Mapping**: When PG events are received with `paymentSystem = GV` for user participants, they are automatically mapped to `paymentSystem = MGV`
- **Context Setting**: MGV redemption context (`TXN_PURPOSE = MGV_REDEEM`) is automatically set for MGV transactions from PG events

```java
// Map GV payment system to MGV for user participants in PG events
if (PaymentSystemEnum.GV.getPaymentSystemKey().equals(participants.getPaymentSystem())
    && EntityTypesEnum.USER.equals(participants.getEntityType())) {
    transformedParticipant.setPaymentSystem(PaymentSystemEnum.MGV.getPaymentSystemKey());
    
    // Set MGV redemption context
    Map<String, String> contextMap = transformedParticipant.getContextMap();
    if (contextMap == null) {
        contextMap = new HashMap<>();
        transformedParticipant.setContextMap(contextMap);
    }
    contextMap.put(TXN_PURPOSE, MGV_REDEEM);
}
```

### 2. Merchant Data Handling

The MGV PG integration automatically handles merchant data extraction during transaction processing:

- **Merchant VPA**: Available in `participant.upiData.vpa` for merchant participants (`entityType = MERCHANT`)
- **Merchant ID**: Available in `participant.merchantData.merchantID` for merchant participants (`entityType = MERCHANT`)
- **Automatic Processing**: Merchant data is processed as part of the standard transaction flow without requiring additional utility methods

## Transaction Flow

### MGV Redemption from PG
1. **PG Event Received**: Transaction comes with `paymentSystem = GV` for user participant
2. **Payment System Mapping**: `GV` is automatically mapped to `MGV` for user participants
3. **Context Setting**: `TXN_PURPOSE = MGV_REDEEM` is set in participant context
4. **Merchant Data Processing**:
   - Merchant VPA available in `participant.upiData.vpa` (merchant lag)
   - Merchant ID available in `participant.merchantData.merchantID` (merchant lag)
5. **Processing**: Transaction processed as standard MGV redemption with merchant data

### Data Structure
```
User Participant (entityType = USER):
- paymentSystem: MGV (mapped from GV)
- contextMap: { "TXN_PURPOSE": "MGV_REDEEM" }

Merchant Participant (entityType = MERCHANT):
- upiData.vpa: "merchant@paytm" (merchant VPA)
- merchantData.merchantId: "MERCHANT_123" (merchant ID)
```

## Testing

### Test Coverage
The MGV PG integration is covered by existing MGV test suites:

- **MgvUtilityTest**: Core MGV functionality tests
- **OmsPaymentMethodEnumTest**: MGV payment method enum tests
- **OmsRefundPaymentMethodEnumTest**: MGV refund payment method enum tests

### Running Tests
```bash
mvn test -Dtest="*MgvUtilityTest,*OmsPaymentMethodEnumTest,*OmsRefundPaymentMethodEnumTest"
```

## Integration Points

### 1. PG Event Processing
- **Input**: PG events with `paymentSystem = GV` for user participants
- **Output**: Transformed events with `paymentSystem = MGV` and proper context

### 2. Merchant Data Requirements
- **Merchant VPA**: Available in `participant.upiData.vpa` for merchant participants
- **Merchant ID**: Available in `participant.merchantData.merchantID` for merchant participants
- **Validation**: At least one of VPA or MID must be present for valid merchant data

### 3. Backward Compatibility
- Existing MGV transactions continue to work unchanged
- Direct MGV payment system events are still supported
- No breaking changes to existing functionality

## Configuration

### Payment System Enums
- **GV**: Payment system key = 13 (existing)
- **MGV**: Payment system key = 6 (existing)

### Transaction Purpose Constants
- **MGV_REDEEM**: "MGV_REDEEM" (for PG redemption events)
- **MGV_PURCHASED**: "MGV_PURCHASED" (for OMS purchase events)

## Error Handling

### Invalid Merchant Data
- If merchant participant has neither VPA nor MID, `isValidMgvMerchantData()` returns false
- Utility methods return null for missing data rather than throwing exceptions
- Graceful degradation for incomplete merchant information

### Missing Participants
- All utility methods handle null transaction inputs
- Empty participant lists are handled gracefully
- No exceptions thrown for missing data

## Performance Considerations

- **Minimal Overhead**: Payment system mapping adds negligible processing time
- **Efficient Filtering**: Merchant participant lookup uses stream filtering
- **Memory Efficient**: Context maps created only when needed
- **No Additional Database Calls**: All data extracted from existing transaction structure

## Monitoring and Logging

### Key Metrics to Monitor
- MGV transaction volume from PG events
- Payment system mapping success rate
- Merchant data extraction success rate
- Transaction processing latency for MGV

### Log Points
- Payment system mapping from GV to MGV
- MGV context setting for PG events
- Merchant data extraction results
- Validation failures for merchant data

## Future Enhancements

### Potential Improvements
1. **Enhanced Validation**: More sophisticated merchant data validation rules
2. **Metrics Collection**: Detailed metrics on MGV PG transaction patterns
3. **Error Recovery**: Automatic retry mechanisms for failed merchant data extraction
4. **Performance Optimization**: Caching of frequently accessed merchant data

### Extensibility
- Architecture supports additional payment instruments without code changes
- Merchant data extraction pattern can be extended for other transaction types
- Context creation mechanism can be enhanced for additional metadata

## Conclusion

The MGV PG integration successfully handles merchant gift voucher redemption transactions from PG events while maintaining compatibility with existing systems. The implementation follows established patterns and provides comprehensive merchant data handling capabilities.
